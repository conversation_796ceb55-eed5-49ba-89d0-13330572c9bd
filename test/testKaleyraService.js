import dotenv from 'dotenv';
import KaleyraWhatsAppService from '../service/KaleyraWhatsAppService.js';

dotenv.config();

const kaleyraWhatsAppService = new KaleyraWhatsAppService();

const testPacket = {
  transactionId: `test-${Date.now()}`,
  recipients: [
    {
      name: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      whatsappNumber: '7838846910',
      mobileNumber: '7838846910',
    },
  ],
  channel: 'whatsapp',
  content: {
    title: 'Test Alert',
    body: 'This is a test alert message',
  },
  ts: new Date().toISOString(),
  metadata: {
    incidentId: '12345',
    alertInventoryId: '123',
    eventName: 'RESOLVED',
    deviceId: 'device-001',
    deviceType: 'Device Type 1',
    assetName: 'Asset 1',
    siteId: 'ash-tri',
    siteName: 'Site 1',
    severity: 'high',
    alertType: 'cpa',
    observer_execution_ref_id: 'obs-001',
    timestamp: new Date().toISOString(),
    timestampOccurred: new Date().toISOString(),
    templateId: null,
  },
};

try {
  const service = new KaleyraWhatsAppService();
  await service.sendNotification(testPacket);
  console.log('🏁 Test completed');
  process.exit(0);
} catch (error) {
  console.error('❌ Test failed:', error.message);
  if (error.response?.data) {
    console.error(
      'API Error Details:',
      JSON.stringify(error.response.data, null, 2),
    );
  }
  process.exit(1);
}
