{"name": "escalation-trigger-microservice", "version": "1.0.0", "description": "Microservice that handles automated alert escalation for unresolved incidents", "type": "module", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "lint": "eslint --fix src/**/*.js", "test": "jest"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.350.0", "@aws-sdk/lib-dynamodb": "^3.350.0", "@sentry/node": "^7.52.1", "dotenv": "^16.0.3", "joi": "^17.9.2", "kafkajs": "^2.2.4", "kafkajs-snappy": "^1.1.0", "moment-timezone": "^0.5.43", "node-cron": "^3.0.2", "pg": "^8.11.0", "redis": "^4.6.6", "uuid": "^9.0.0", "winston": "^3.8.2", "winston-transport-sentry-node": "^2.7.0"}, "devDependencies": {"eslint": "^8.40.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5", "jest": "^29.5.0", "nodemon": "^2.0.22"}, "engines": {"node": ">=18.0.0"}}