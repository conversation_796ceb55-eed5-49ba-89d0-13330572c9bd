import 'dotenv/config';
import * as Sentry from '@sentry/node';
import kafkaConnection from './connection/kafka.js';
import { isValidMessagePacket } from './helper/index.js';
import KaleyraWhatsAppService from './service/KaleyraWhatsAppService.js';
import kafkaConfig from './config/kafka.js';
import logger from './utils/logger.js';

async function onMessageHandler(message) {
  try {
    if (!isValidMessagePacket(message)) {
      return;
    }

    if (message.recipients.length === 0) return;

    const whatsappService = new KaleyraWhatsAppService();
    await whatsappService.sendNotification(message);
  } catch (error) {
    error.message = `Message processing error: ${error.message}`;
    throw error;
  }
}

const initializeService = async () => {
  try {
    await kafkaConnection.connect();
    await kafkaConnection.subscribe(
      kafkaConfig.topics.whatsappNotification,
      onMessageHandler,
    );
    logger.info('🧩 Whatsapp notification service started successfully');
  } catch (error) {
    logger.error(`Failed to initialize whatsapp notification service`, {
      error: error.message,
    });
    await Sentry.flush();
    process.exit(1);
  }
};

const gracefulShutdown = async () => {
  try {
    await Sentry.flush();
    await kafkaConnection.disconnect();
    logger.info('Whatsapp notification service shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error(`Error during whatsapp notification service shutdown`, {
      error,
    });
    process.exit(1);
  }
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

initializeService();
