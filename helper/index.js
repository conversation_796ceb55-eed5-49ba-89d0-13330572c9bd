import Joi from 'joi';
import logger from '../utils/logger.js';

export function isValidMessagePacket(rawMessageObject) {
  const schema = Joi.object({
    transactionId: Joi.string().required(),
    recipients: Joi.array()
      .items(
        Joi.object({
          name: Joi.string(),
          email: Joi.string()
            .email({ tlds: { allow: false } })
            .required(),
          whatsappNumber: Joi.string(),
          mobileNumber: Joi.string(),
        }).unknown(true),
      )
      .required(),
    channel: Joi.string().valid('whatsapp').required(),
    content: Joi.object({
      title: Joi.string().required(),
      body: Joi.string().required(),
    })
      .unknown(true)
      .required(),
    ts: Joi.string().isoDate().required(),
    metadata: Joi.object({
      incidentId: Joi.alternatives().try(Joi.string(), Joi.number()).required(),
      alertInventoryId: Joi.alternatives()
        .try(Joi.string(), Joi.number())
        .required(),
      eventName: Joi.string().required(),
      deviceId: Joi.string().required(),
      deviceType: Joi.string().required(),
      assetName: Joi.string().required(),
      siteId: Joi.string().required(),
      siteName: Joi.string().required(),
      severity: Joi.string().required(),
      alertType: Joi.string().required(),
      observer_execution_ref_id: Joi.string().required(),
      timestamp: Joi.string().isoDate().required(),
      timestampOccurred: Joi.string().isoDate().allow('').allow(null),
    })
      .unknown(true)
      .required(),
  }).unknown(true);

  const { error } = schema.validate(rawMessageObject);
  if (error) {
    logger.warn(`Invalid message packet`, { error });
    return false;
  }
  return true;
}
