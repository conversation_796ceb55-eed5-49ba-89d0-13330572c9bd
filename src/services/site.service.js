import redisClient from '../connections/redis.js';
import { dynamoDbClient } from '../connections/dynamodb.js';
import logger from '../utils/logger.js';

// Random TTL between 23-24 hours to prevent cache stampede
const getRandomDayTTL = () => Math.floor(23 * 60 * 60 + Math.random() * 60 * 60);

class SiteService {
  async getSiteNameBySiteId(siteId) {
    const cacheKey = `alert:site:name:${siteId}`;

    try {
      // Try to get from cache first
      const cachedSiteName = await redisClient.get(cacheKey);
      if (cachedSiteName) {
        logger.debug(`Site name found in cache for siteId: ${siteId}`);
        return cachedSiteName;
      }

      // If not in cache, get from DynamoDB
      const siteName = await dynamoDbClient.getSiteNameBySiteId(siteId);

      // Cache with TTL
      const ttl = getRandomDayTTL();
      await redisClient.set(cacheKey, siteName);
      await redisClient.expire(cacheKey, ttl);

      logger.debug(`Site name fetched from DynamoDB for siteId: ${siteId}`);
      return siteName;
    } catch (error) {
      logger.error(`Failed to get site name for siteId ${siteId}: ${error.message}`, { error });
      return siteId;
    }
  }
}

export const siteService = new SiteService();
