import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import kafkaConnection from '../connections/kafka.js';
import { kafkaConfig } from '../config/index.js';
import { alertRepository } from '../repositories/alert.repository.js';
import { withLock } from './lock.service.js';
import { siteService } from './site.service.js';
import { userService } from './user.service.js';
import logger from '../utils/logger.js';

class AlertService {
  async checkAndEscalateAlerts() {
    logger.info('Starting alert escalation check');
    const now = moment().tz('UTC').toISOString();
    let incidentsToEscalate;

    try {
      incidentsToEscalate = await alertRepository.getIncidentsToEscalate();
      logger.info(`Found ${incidentsToEscalate.length} incidents that need escalation`);
    } catch (error) {
      logger.error(`Failed to fetch incidents to escalate: ${error.message}`, { error });
      throw error;
    }

    for (const incident of incidentsToEscalate) {
      const logContext = {
        incidentId: incident.id,
        alertInventoryId: incident.alert_inventory_id,
        siteId: incident.siteid,
      };

      logger.info(`Processing incident for escalation`, logContext);

      const lockKey = `alert:escalation_lock:${incident.id}`;
      try {
        await withLock(lockKey, async () => {
          await alertRepository.updateIncidentEscalation(incident.id, now);

          const notifications = await this.prepareEscalationNotifications(incident);

          await this.sendEscalationNotifications(notifications);
        });
      } catch (error) {
        logger.error(`Failed to process incident ${incident.id} for escalation: ${error.message}`, {
          ...logContext,
          error,
        });
      }
    }
  }

  async prepareEscalationNotifications(incident) {
    const siteName = await siteService.getSiteNameBySiteId(incident.siteid);
    const transactionId = `escalation-trigger-service_${uuidv4()}`;

    logger.debug('Preparing escalation notifications', {
      incidentId: incident.id,
      siteId: incident.siteid,
      transactionId,
    });

    const recipients = await userService.getRecipientsFromEmailList(incident.escalated_to);
    if (recipients.length === 0) {
      logger.warn(`No valid recipients found for incident ${incident.id}`);
      return [];
    }

    const notifications = [];

    await this.prepareEmailNotification(incident, recipients, siteName, transactionId, notifications);

    await this.prepareWhatsAppNotification(incident, recipients, siteName, transactionId, notifications);

    return notifications;
  }

  async prepareEmailNotification(incident, recipients, siteName, transactionId, notifications) {
    // Try to get title and description from notification_message_template
    let title = incident.name;
    let body = incident.description;

    try {
      const template = await alertRepository.getNotificationMessageTemplate(
        incident.alert_inventory_id,
        incident.alert_template_ref_id,
        'email',
        'OCCURRED',
      );

      if (template) {
        title = template.title || title;
        body = template.description || body;
      }
    } catch (error) {
      logger.warn(`Failed to fetch email notification template, using default title/description: ${error.message}`, {
        error,
        incidentId: incident.id,
      });
    }

    const emailNotification = {
      channel: 'email',
      recipients,
      content: {
        title,
        body,
      },
      metadata: {
        incidentId: incident.id,
        alertInventoryId: incident.alert_inventory_id,
        siteId: incident.siteid,
        siteName,
        eventName: 'ESCALATED',
        severity: incident.severity,
        alertType: incident.alert_category || '',
        deviceId: incident.asset_id || '',
        deviceType: incident.asset_type || '',
        assetName: incident.name,
        observer_execution_ref_id: incident.observer_execution_ref_id || '',
        timestamp: moment(incident.issue_occurred_at).tz('UTC').toISOString(),
        timestampOccurred: null,
      },
      ts: moment().tz('UTC').toISOString(),
      transactionId,
    };

    notifications.push(emailNotification);
  }

  async prepareWhatsAppNotification(incident, recipients, siteName, transactionId, notifications) {
    // Filter recipients who have WhatsApp numbers
    const whatsappRecipients = recipients.filter((recipient) => recipient.whatsappNumber || recipient.mobileNumber);

    if (whatsappRecipients.length === 0) {
      logger.debug(`No recipients with WhatsApp numbers found for incident ${incident.id}`);
      return;
    }

    // Try to get title and description from WhatsApp notification template
    let title = incident.name;
    let body = incident.description;

    try {
      const template = await alertRepository.getNotificationMessageTemplate(
        incident.alert_inventory_id,
        incident.alert_template_ref_id,
        'whatsapp',
        'OCCURRED',
      );

      if (template) {
        title = template.title || title;
        body = template.description || body;
      }
    } catch (error) {
      logger.warn(`Failed to fetch WhatsApp notification template, using default title/description: ${error.message}`, {
        error,
        incidentId: incident.id,
      });
    }

    const whatsappNotification = {
      channel: 'whatsapp',
      recipients: whatsappRecipients,
      content: {
        title,
        body,
      },
      metadata: {
        incidentId: incident.id,
        alertInventoryId: incident.alert_inventory_id,
        siteId: incident.siteid,
        siteName,
        eventName: 'ESCALATED',
        severity: incident.severity,
        alertType: incident.alert_category || '',
        deviceId: incident.asset_id || '',
        deviceType: incident.asset_type || '',
        assetName: incident.name,
        observer_execution_ref_id: incident.observer_execution_ref_id || '',
        timestamp: moment(incident.issue_occurred_at).tz('UTC').toISOString(),
        timestampOccurred: null,
      },
      ts: moment().tz('UTC').toISOString(),
      transactionId,
    };

    notifications.push(whatsappNotification);
  }

  async sendEscalationNotifications(notifications) {
    if (!notifications || notifications.length === 0) {
      logger.warn('No notifications to send');
      return;
    }

    const results = [];

    for (const notification of notifications) {
      try {
        const topic =
          notification.channel === 'whatsapp' ? kafkaConfig.KAFKA_TOPIC_WHATSAPP : kafkaConfig.KAFKA_TOPIC_EMAIL;

        await kafkaConnection.sendMessage(topic, notification);

        logger.info(`🚀 Alert escalation notification sent`, {
          notification,
        });

        results.push({ channel: notification.channel, status: 'success' });
      } catch (error) {
        logger.error(`🤡 Failed to publish escalation notification: ${error.message}`, {
          error,
          notification,
        });

        results.push({ channel: notification.channel, status: 'failed', error: error.message });
      }
    }

    const successCount = results.filter((r) => r.status === 'success').length;
    const failureCount = results.filter((r) => r.status === 'failed').length;

    logger.info('Alert escalation batch complete', {
      total: notifications.length,
      success: successCount,
      failed: failureCount,
      results,
    });

    // Throw error only if all notifications failed
    if (failureCount === notifications.length) {
      throw new Error('All escalation notifications failed to send');
    }
  }
}

export const alertService = new AlertService();
