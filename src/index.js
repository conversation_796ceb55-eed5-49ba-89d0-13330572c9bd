import 'dotenv/config';
import PostgresConnection from './connections/postgres.js';
import redisClient from './connections/redis.js';
import kafkaConnection from './connections/kafka.js';
import { startAlertEscalationJob } from './jobs/alert-escalation.job.js';
import logger from './utils/logger.js';
import { NODE_ENV } from './config/index.js';

logger.info(`Starting escalation-trigger-microservice in ${NODE_ENV} environment`);

/**
 * Initialize and start the worker
 */
async function startWorker() {
  try {
    // Initialize connections
    const postgres = new PostgresConnection();
    await kafkaConnection.connect();

    // Start the escalation job
    const job = startAlertEscalationJob();

    logger.info('Escalation worker started successfully');

    return { postgres, job };
  } catch (error) {
    logger.error(`Failed to start escalation worker: ${error.message}`, { error });
    process.exit(1);
  }
}

/**
 * Handle graceful shutdown
 */
async function gracefulShutdown(signal, resources) {
  logger.info(`Received ${signal}, starting graceful shutdown`);

  try {
    // Stop the cron job if it exists
    if (resources?.job) {
      resources.job.stop();
      logger.info('Escalation job stopped');
    }

    // Disconnect from Kafka
    await kafkaConnection.disconnect();
    logger.info('Kafka connection closed');

    // Close PostgreSQL connection
    if (resources?.postgres) {
      await resources.postgres.close();
      logger.info('PostgreSQL connection closed');
    }

    // Close Redis connection
    await redisClient.quit();
    logger.info('Redis connection closed');

    logger.info('Escalation worker shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error(`Error during escalation worker shutdown: ${error.message}`, { error });
    process.exit(1);
  }
}

// Start the worker and set up shutdown handlers
const resources = await startWorker();

// Handle signals for graceful shutdown
process.on('SIGTERM', () => gracefulShutdown('SIGTERM', resources));
process.on('SIGINT', () => gracefulShutdown('SIGINT', resources));
