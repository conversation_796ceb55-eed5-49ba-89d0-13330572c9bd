import kafkajs from 'kafkajs';
import SnappyCodec from 'kafkajs-snappy';
import { v4 as uuidv4 } from 'uuid';
import { kafkaConfig } from '../config/index.js';
import logger from '../utils/logger.js';

const { Kafka, CompressionTypes, CompressionCodecs } = kafkajs;
CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;

class KafkaConnection {
  constructor() {
    if (KafkaConnection.instance) {
      return KafkaConnection.instance;
    }
    KafkaConnection.instance = this;
    this.client = null;
    this.producer = null;
    this.isConnected = false;
  }

  async initialize() {
    const { KAFKA_SECURITY_PROTOCOL, KAFKA_BROKERS, KAFKA_USERNAME, KAFKA_PASSWORD, CONNECTION_TIMEOUT } = kafkaConfig;

    if (KAFKA_SECURITY_PROTOCOL === 'SASL') {
      this.client = new Kafka({
        clientId: `alert-escalation-producer-${uuidv4()}`,
        brokers: KAFKA_BROKERS,
        ssl: true,
        connectionTimeout: CONNECTION_TIMEOUT,
        sasl: {
          mechanism: 'scram-sha-512',
          username: KAFKA_USERNAME,
          password: KAFKA_PASSWORD,
        },
      });
    } else if (KAFKA_SECURITY_PROTOCOL === 'PLAIN') {
      this.client = new Kafka({
        clientId: `alert-escalation-producer-${uuidv4()}`,
        brokers: KAFKA_BROKERS,
        connectionTimeout: CONNECTION_TIMEOUT,
      });
    } else {
      throw new Error('Only PLAIN and SASL security protocol supported');
    }

    this.producer = this.client.producer();
  }

  async connect() {
    if (this.isConnected) {
      return;
    }

    try {
      if (!this.producer) {
        await this.initialize();
      }
      await this.producer.connect();
      this.isConnected = true;
      logger.info('Kafka producer connected successfully');
    } catch (error) {
      this.isConnected = false;
      logger.error('Failed to connect Kafka producer', { error });
      throw error;
    }
  }

  async getProducer() {
    if (!this.isConnected) {
      await this.connect();
    }
    return this.producer;
  }

  async disconnect() {
    if (!this.isConnected) {
      return;
    }

    try {
      await this.producer.disconnect();
      this.isConnected = false;
      logger.info('Kafka producer disconnected successfully');
    } catch (error) {
      logger.error('Failed to disconnect Kafka producer', { error });
      throw error;
    }
  }

  async sendMessage(topic, message) {
    try {
      const producer = await this.getProducer();
      await producer.send({
        topic,
        compression: CompressionTypes.Snappy,
        messages: [{ value: JSON.stringify(message) }],
      });
    } catch (error) {
      logger.error('Failed to send message to Kafka', { error, topic });
      throw error;
    }
  }
}

const kafkaConnection = new KafkaConnection();
export default kafkaConnection;
