import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { dynamoDbConfig } from '../config/index.js';
import logger from '../utils/logger.js';

class DynamoDB {
  constructor() {
    const clientConfig = {
      region: dynamoDbConfig.REGION,
    };

    const client = new DynamoDBClient(clientConfig);
    this.docClient = DynamoDBDocumentClient.from(client);
  }

  async query(tableName, keyConditionExpression, expressionAttributeValues) {
    try {
      const params = {
        TableName: tableName,
        KeyConditionExpression: keyConditionExpression,
        ExpressionAttributeValues: expressionAttributeValues,
      };

      logger.debug(`Querying DynamoDB table=${tableName}`, { keyConditionExpression });
      const result = await this.docClient.send(new QueryCommand(params));
      return result.Items || [];
    } catch (error) {
      logger.error(`Failed to query DynamoDB table=${tableName}: ${error.message}`, {
        error,
        keyConditionExpression,
        expressionAttributeValues
      });
      return [];
    }
  }

  async getUserByEmail(email) {
    try {
      const results = await this.query('users', 'userId = :email', { ':email': email });
      return results[0] || null;
    } catch (error) {
      logger.error(`Failed to fetch user for email=${email}: ${error.message}`, { error });
      return null;
    }
  }

  async getSiteNameBySiteId(siteId) {
    try {
      const results = await this.query('sites', 'siteId = :id', { ':id': siteId });
      return results[0]?.siteName || siteId;
    } catch (error) {
      logger.error(`Failed to fetch site name for siteId=${siteId}: ${error.message}`, { error });
      return siteId;
    }
  }
}

export const dynamoDbClient = new DynamoDB();
