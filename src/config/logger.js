import { createLogger, format, transports } from 'winston';
import * as Sen<PERSON> from '@sentry/node';
import SentryTransportModule from 'winston-transport-sentry-node';

const SentryTransport = SentryTransportModule.default || SentryTransportModule;

Sentry.init({
  dsn: process.env.SENTRY_DSN,
});

const logger = createLogger({
  format: format.combine(
    format.timestamp(),
    format.json()
  ),
  transports: [
    new transports.Console({
      level: process.env.LOG_LEVEL || 'info',
    }),
    new SentryTransport({
      sentry: Sentry,
      level: 'error',
    }),
  ],
  exitOnError: false,
});

export default logger;
