import PostgresConnection from '../connections/postgres.js';
import logger from '../utils/logger.js';

class AlertRepository {
  constructor() {
    this.postgres = new PostgresConnection();
  }

  async getIncidentsToEscalate() {
    const query = `
      SELECT
        aih.id,
        aih.uuid,
        aih.issue_occurred_at,
        aih.escalated_at,
        ai.id as alert_inventory_id,
        ai.name,
        COALESCE(ai.description, at.description) as description,
        ai.siteid,
        COALESCE(ai.severity, at.severity) as severity,
        ai.asset_id,
        ai.asset_type,
        COALESCE(ai.escalation_time_in_min, at.escalation_time_in_min) as escalation_time_in_min,
        COALESCE(ai.escalated_to::jsonb, at.escalated_to::jsonb, '[]'::jsonb) as escalated_to,
        ai.alert_template_ref_id,
        at.name as template_name,
        at.alert_category,
        at.observer_execution_ref_id
      FROM alert_incident_history aih
      JOIN alert_inventory ai ON aih.alert_inventory_id = ai.id
      JOIN alert_template at ON ai.alert_template_ref_id = at.id
      WHERE aih.escalated_at IS NULL
        AND aih.acknowledge_ts IS NULL
        AND aih.issue_resolved_at IS NULL
        AND COALESCE(ai.escalation_time_in_min, at.escalation_time_in_min) > 0
        AND (
          COALESCE(ai.escalated_to::jsonb, at.escalated_to::jsonb) IS NOT NULL
          AND jsonb_array_length(COALESCE(ai.escalated_to::jsonb, at.escalated_to::jsonb, '[]'::jsonb)) > 0
        )
        AND aih.issue_occurred_at AT TIME ZONE 'UTC' <= (CURRENT_TIMESTAMP AT TIME ZONE 'UTC') -
            (COALESCE(ai.escalation_time_in_min, at.escalation_time_in_min) * INTERVAL '1 minute')
    `;

    try {
      const { rows } = await this.postgres.query(query);

      // Ensure escalated_to is an array
      const processedRows = rows.map(row => ({
        ...row,
        escalated_to: Array.isArray(row.escalated_to) ? row.escalated_to : []
      }));

      return processedRows;
    } catch (error) {
      logger.error(`Failed to fetch incidents to escalate: ${error.message}`, { error });
      throw error;
    }
  }

  async updateIncidentEscalation(incidentId, escalatedAt) {
    const query = `
      UPDATE alert_incident_history
      SET
        escalated_at = $1,
        updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
      WHERE id = $2
      RETURNING
        id,
        alert_inventory_id,
        issue_occurred_at,
        escalated_at
    `;

    try {
      const { rows } = await this.postgres.query(query, [escalatedAt, incidentId]);
      if (rows.length === 0) {
        throw new Error(`No incident found with ID: ${incidentId}`);
      }
      logger.info(`Updated incident escalation status`, { incidentId, escalatedAt });
      return rows[0];
    } catch (error) {
      logger.error(`Failed to update incident escalation: ${error.message}`, { incidentId, error });
      throw error;
    }
  }

  async getNotificationMessageTemplate(alertInventoryId, alertTemplateId, channel = 'email', eventType = 'OCCURRED') {
    const query = `
      SELECT
        id,
        title,
        description
      FROM notification_message_template
      WHERE
        (alert_inventory_ref_id = $1 OR alert_template_ref_id = $2)
        AND channel = $3
        AND event_type = $4
        AND status = 1
      ORDER BY
        CASE WHEN alert_inventory_ref_id = $1 THEN 0 ELSE 1 END
      LIMIT 1
    `;

    try {
      const { rows } = await this.postgres.query(query, [alertInventoryId, alertTemplateId, channel, eventType]);
      if (rows.length > 0) {
        logger.debug(`Found notification message template with ID ${rows[0].id}`, {
          alertInventoryId,
          alertTemplateId
        });
        return rows[0];
      }

      logger.debug(`No notification message template found`, {
        alertInventoryId,
        alertTemplateId,
        channel,
        eventType
      });
      return null;
    } catch (error) {
      logger.error(`Failed to fetch notification message template: ${error.message}`, {
        error,
        alertInventoryId,
        alertTemplateId
      });
      return null;
    }
  }
}

export const alertRepository = new AlertRepository();
