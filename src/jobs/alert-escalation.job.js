import nodeCron from 'node-cron';
import { alertService } from '../services/alert.service.js';
import logger from '../utils/logger.js';

/**
 * Starts the alert escalation cron job
 * Runs every minute to check for alerts that need to be escalated
 */
export const startAlertEscalationJob = () => {
  logger.info('Alert escalation job scheduled to run every minute');

  const job = nodeCron.schedule('* * * * *', async () => {
    const jobId = `escalation-job-${Date.now()}`;

    logger.info('Running alert escalation job', { jobId });

    try {
      await alertService.checkAndEscalateAlerts();
      logger.info('Alert escalation job completed successfully', { jobId });
    } catch (error) {
      logger.error(`Alert escalation job failed: ${error.message}`, {
        error,
        jobId,
        stack: error.stack,
      });
    }
  });

  // Handle graceful shutdown
  process.on('SIGTERM', () => {
    logger.info('Stopping alert escalation job');
    job.stop();
  });

  return job;
};
